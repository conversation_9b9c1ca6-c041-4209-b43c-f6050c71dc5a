{"doc": " 项目同步Service业务层处理\n\n <AUTHOR>\n @date 2025-07-28\n", "fields": [], "enumConstants": [], "methods": [{"name": "validateConstructionPermitNo", "paramTypes": ["java.lang.String"], "doc": " 验证施工许可证编号\n"}, {"name": "validateConstructionPermission", "paramTypes": ["java.lang.String"], "doc": " 验证施工方权限\n 如果当前用户是施工方，需要校验该项目是否属于他们\n"}, {"name": "isConstructionUser", "paramTypes": ["java.lang.Long"], "doc": " 判断当前用户是否为施工方\n"}, {"name": "validateSyncData", "paramTypes": ["org.dromara.projects.domain.dto.sync.SyncResponseDto"], "doc": " 验证同步数据的完整性\n"}, {"name": "processProjectInfo", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 处理项目基本信息\n"}, {"name": "processAreaInfo", "paramTypes": ["org.dromara.projects.domain.vo.PrjProjectsVo", "java.lang.String", "java.lang.String"], "doc": " 处理地区信息\n\n @param projectVo 项目对象\n @param cityName 市名称（接口返回的XMSZS字段）\n @param districtName 区名称（接口返回的XMSZQ字段）\n"}, {"name": "getCityCodeByName", "paramTypes": ["java.lang.String"], "doc": " 根据市名称获取市编码\n\n @param cityName 市名称\n @return 市编码\n"}, {"name": "getDistrictCodeByName", "paramTypes": ["java.lang.String", "java.lang.String"], "doc": " 根据区名称和市编码获取区编码\n\n @param districtName 区名称\n @param cityCode     市编码\n @return 区编码\n"}, {"name": "processAndSaveCompanyInfo", "paramTypes": ["java.util.List", "java.lang.String"], "doc": " 处理并保存参建单位信息\n"}, {"name": "aggregateCompanyTypes", "paramTypes": ["java.util.List"], "doc": " 聚合同一企业的多个类型记录\n"}, {"name": "saveOrUpdateEnterpriseWithSync", "paramTypes": ["org.dromara.projects.domain.dto.sync.CjdwDto"], "doc": " 保存或更新企业信息（同步版本，整合webAdd和audit逻辑）\n\n @return 返回部门ID（dept_id）\n"}, {"name": "createEnterpriseFromDtoWithSync", "paramTypes": ["org.dromara.projects.domain.dto.sync.CjdwDto"], "doc": " 从DTO创建企业实体（同步版本，整合webAdd逻辑）\n"}, {"name": "updateEnterpriseFromDtoWithMultiTypes", "paramTypes": ["org.dromara.system.domain.SysEnterpriseInfo", "org.dromara.projects.domain.dto.sync.CjdwDto"], "doc": " 从DTO更新企业信息（支持多类型）\n"}, {"name": "executeEnterpriseAuditLogic", "paramTypes": ["org.dromara.system.domain.SysEnterpriseInfo"], "doc": " 执行企业审核通过逻辑（整合audit接口逻辑）\n"}, {"name": "createOrUpdateDepartment", "paramTypes": ["org.dromara.system.domain.SysEnterpriseInfo"], "doc": " 创建或更新部门信息（整合audit接口中的部门创建逻辑）\n"}, {"name": "createEnterpriseUser", "paramTypes": ["org.dromara.system.domain.SysEnterpriseInfo"], "doc": " 创建企业用户（整合audit接口中的用户创建逻辑）\n"}, {"name": "getRoleIdsByEnterpriseTypes", "paramTypes": ["java.lang.String"], "doc": " 根据企业类型获取角色ID数组（支持多类型）\n"}, {"name": "mapCorpTypeToEnterpriseType", "paramTypes": ["java.lang.String"], "doc": " 映射企业类型\n"}, {"name": "updateProjectCompanyReferences", "paramTypes": ["org.dromara.projects.domain.vo.PrjProjectsVo", "java.util.Map"], "doc": " 更新项目的单位关联信息（设置部门ID）\n"}, {"name": "processPersonnelInfo", "paramTypes": ["org.dromara.projects.domain.vo.PrjProjectsVo", "java.util.List", "java.util.Map"], "doc": " 处理人员信息\n\n @param projectVo 项目信息\n @param xgryList 外部API返回的人员信息列表\n @param companyDeptIdMap 企业类型到部门ID的映射\n @return 人员ID到角色的映射，用于后续绑定项目人员关联\n"}, {"name": "processIndividualPersonnel", "paramTypes": ["org.dromara.projects.domain.dto.sync.XgryDto", "java.util.Map"], "doc": " 处理单个人员信息\n\n @param xgry             外部API返回的人员信息\n @param companyDeptIdMap 企业类型到部门ID的映射\n @return 人员ID，如果处理失败返回null\n"}, {"name": "createPersonFromXgry", "paramTypes": ["org.dromara.projects.domain.dto.sync.XgryDto", "java.util.Map"], "doc": " 从外部API数据创建新人员\n"}, {"name": "updatePersonFromXgry", "paramTypes": ["org.dromara.person.domain.vo.SysPersonVo", "org.dromara.projects.domain.dto.sync.XgryDto", "java.util.Map"], "doc": " 更新现有人员信息\n"}, {"name": "findEnterpriseIdByName", "paramTypes": ["java.lang.String", "java.util.Map"], "doc": " 根据企业名称查找企业ID\n"}, {"name": "mapPersonTypeToProjectRole", "paramTypes": ["java.lang.String"], "doc": " 映射人员类型到项目角色\n"}, {"name": "bindProjectPersonnelFromSync", "paramTypes": ["java.lang.Long", "java.util.Map"], "doc": " 绑定项目人员关联（同步版本）\n\n @param projectId        项目ID\n @param personnelRoleMap 人员ID到角色的映射\n"}, {"name": "processEquipmentInfo", "paramTypes": ["org.dromara.projects.domain.vo.PrjProjectsVo", "java.util.List"], "doc": " 处理设备信息\n"}, {"name": "saveOrUpdateProject", "paramTypes": ["org.dromara.projects.domain.vo.PrjProjectsVo"], "doc": " 保存或更新项目信息\n"}, {"name": "createProjectBoFromVo", "paramTypes": ["org.dromara.projects.domain.vo.PrjProjectsVo"], "doc": " 从 PrjProjectsVo 创建 PrjProjectsBo 对象\n"}, {"name": "parseDate", "paramTypes": ["java.lang.String"], "doc": " 解析日期字符串\n"}], "constructors": []}